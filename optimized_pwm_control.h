// optimized_pwm_control.h
// 针对无过零检测的可控硅PWM控制优化方案
#ifndef OPTIMIZED_PWM_CONTROL_H
#define OPTIMIZED_PWM_CONTROL_H

#include <Arduino.h>
#include "user_config.h"  // 引用用户配置中的引脚定义

// 光耦芯片类型定义
enum OptocouplerType {
    MOC3020_TYPE = 0,  // 标准MOC3020
    MOC3063_TYPE = 1   // 新的MOC3063
};

// 优化的PWM控制类 - 支持多种光耦芯片
class OptimizedPWMControl {
private:
    int target_power = 0;               // 目标功率百分比(0-100)
    int pwm_channel = 5;                // PWM通道
    OptocouplerType optocoupler_type = MOC3063_TYPE;  // 当前光耦类型
    
    // 优化的PWM参数 - 针对可控硅特性调整
    int pwm_frequency = 1;              // 1Hz超低频PWM，适合可控硅控制
    int pwm_resolution = 12;            // 12位分辨率
    
    // 针对PWM控制的线性化查找表 - 支持多种光耦
    float pwm_linearization_table[101]; // 0-100%功率对应的PWM占空比
    float moc3020_table[101];           // MOC3020专用查找表
    float moc3063_table[101];           // MOC3063专用查找表
    
    // 增强的功率平滑控制
    int power_buffer[15] = {0};         // 增加到15点移动平均
    int buffer_index = 0;
    float smoothing_factor = 0.15f;     // 指数移动平均因子
    
    // 功率变化率限制
    int max_power_change_per_cycle = 2; // 每周期最大功率变化2%
    int last_output_power = 0;
    
public:
    OptimizedPWMControl(int channel = 5);
    
    // 初始化PWM控制
    void begin();
    
    // 设置目标功率(0-100%)
    void setPower(int power_percent);
    
    // 获取当前功率
    int getCurrentPower();
    
    // 主循环更新函数
    void update();
    
    // 设置PWM频率
    void setPWMFrequency(int frequency);
    
    // 设置平滑因子
    void setSmoothingFactor(float factor);
    
    // 设置功率变化率限制
    void setPowerChangeLimit(int max_change);
    
    // 光耦芯片切换
    void setOptocouplerType(OptocouplerType type);
    OptocouplerType getOptocouplerType();
    
    // 重置控制器
    void reset();
    
    // 调试信息
    void printDebugInfo();
    
    // 打印线性化查找表
    void printLinearizationTable();
    
    // 公开线性化函数供测试使用
    float linearizePWMPower(int power_percent);
    
    // 公开PWM计算函数供测试使用
    int calculatePWMValue();
    
private:
    // 初始化PWM线性化查找表
    void initializePWMLinearizationTable();
    
    // 初始化MOC3020查找表
    void initializeMOC3020Table();
    
    // 初始化MOC3063查找表
    void initializeMOC3063Table();
    
    // 切换当前使用的查找表
    void switchLinearizationTable();
    
    // 增强的功率平滑处理
    int enhancedPowerSmoothing(int new_power);
    
    // 功率变化率限制
    int limitPowerChangeRate(int new_power);
};

// 优化PWM功率稳定性监控类 - 避免与其他类冲突
class OptimizedPowerStabilityMonitor {
private:
    float power_history[30] = {0};      // 增加历史记录点数
    int history_index = 0;
    float stability_threshold = 1.5f;   // 更严格的稳定性阈值
    
public:
    void addPowerData(float power);
    bool isPowerStable();
    float getPowerVariation();
    float getAveragePower();
    void reset();
};

#endif // OPTIMIZED_PWM_CONTROL_H