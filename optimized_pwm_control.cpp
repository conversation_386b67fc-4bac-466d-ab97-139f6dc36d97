// optimized_pwm_control.cpp
// 针对无过零检测的可控硅PWM控制优化实现
#include "optimized_pwm_control.h"
#include <math.h>

OptimizedPWMControl::OptimizedPWMControl(int channel) {
    pwm_channel = channel;
    optocoupler_type = MOC3063_TYPE;  // 默认使用MOC3063
    initializePWMLinearizationTable();
}

void OptimizedPWMControl::begin() {
    // PWM配置在主程序的setup()中已经完成，这里只需要初始化内部状态
    // 不重复配置PWM，避免API兼容性问题
    
    Serial.println("[OPTIMIZED_PWM] 优化PWM控制已初始化");
    Serial.printf("[OPTIMIZED_PWM] PWM频率: %dHz, 分辨率: %d位\n", pwm_frequency, pwm_resolution);
    Serial.println("[OPTIMIZED_PWM] 特性: 高频PWM + 增强平滑 + 变化率限制");
    Serial.println("[OPTIMIZED_PWM] 注意: PWM通道配置由主程序管理");
}

void OptimizedPWMControl::initializePWMLinearizationTable() {
    Serial.println("[OPTIMIZED_PWM] 初始化多光耦支持系统...");
    
    // 初始化两种光耦的查找表
    initializeMOC3020Table();
    initializeMOC3063Table();
    
    // 根据当前光耦类型设置活动表
    switchLinearizationTable();
}

void OptimizedPWMControl::initializeMOC3020Table() {
    // MOC3020的特性补偿算法（当前已验证的算法）
    for (int i = 0; i <= 100; i++) {
        if (i == 0) {
            moc3020_table[i] = 0.0f;
        } else {
            // 基于实测数据的强力压制算法
            if (i < 10) {
                // 0-10%：扩大死区
                moc3020_table[i] = 0.0f;
            } else if (i <= 20) {
                // 10-20%：极度压制的线性增长
                float range = (i - 10.0f) / 10.0f;
                moc3020_table[i] = range * 0.25f;  // 最大只到25%功率
            } else if (i <= 40) {
                // 20-40%：缓慢增长区域
                float range = (i - 20.0f) / 20.0f;
                moc3020_table[i] = 0.25f + range * 0.25f;  // 25%-50%
            } else if (i <= 70) {
                // 40-70%：中等增长区域
                float range = (i - 40.0f) / 30.0f;
                moc3020_table[i] = 0.50f + range * 0.25f;  // 50%-75%
            } else {
                // 70-100%：最终增长到满功率
                float range = (i - 70.0f) / 30.0f;
                moc3020_table[i] = 0.75f + range * 0.25f;  // 75%-100%
            }
        }
    }
}

void OptimizedPWMControl::initializeMOC3063Table() {
    // MOC3063的特性补偿算法（更敏感的光耦，需要不同的补偿）
    for (int i = 0; i <= 100; i++) {
        if (i == 0) {
            moc3063_table[i] = 0.0f;
        } else {
            // MOC3063通常比MOC3020更敏感，需要更强的压制
            if (i < 8) {
                // 0-8%：更小的死区（MOC3063更敏感）
                moc3063_table[i] = 0.0f;
            } else if (i <= 18) {
                // 8-18%：更强的压制（MOC3063触发更容易）
                float range = (i - 8.0f) / 10.0f;
                moc3063_table[i] = range * 0.20f;  // 最大只到20%功率
            } else if (i <= 35) {
                // 18-35%：缓慢增长
                float range = (i - 18.0f) / 17.0f;
                moc3063_table[i] = 0.20f + range * 0.25f;  // 20%-45%
            } else if (i <= 65) {
                // 35-65%：中等增长
                float range = (i - 35.0f) / 30.0f;
                moc3063_table[i] = 0.45f + range * 0.30f;  // 45%-75%
            } else {
                // 65-100%：最终到满功率
                float range = (i - 65.0f) / 35.0f;
                moc3063_table[i] = 0.75f + range * 0.25f;  // 75%-100%
            }
        }
    }
}

void OptimizedPWMControl::switchLinearizationTable() {
    // 根据当前光耦类型复制对应的查找表
    if (optocoupler_type == MOC3063_TYPE) {
        for (int i = 0; i <= 100; i++) {
            pwm_linearization_table[i] = moc3063_table[i];
        }
    } else {
        for (int i = 0; i <= 100; i++) {
            pwm_linearization_table[i] = moc3020_table[i];
        }
    }
    
    Serial.println("[OPTIMIZED_PWM] 可控硅特性补偿算法已初始化");
    const char* current_optocoupler = (optocoupler_type == MOC3063_TYPE) ? "MOC3063" : "MOC3020";
    Serial.printf("[OPTIMIZED_PWM] 当前光耦: %s - 功率映射：\n", current_optocoupler);
    
    // 显示关键测试点
    int key_points[] = {10, 12, 14, 16, 18, 20, 25, 30, 40, 50, 70, 100};
    int num_points = sizeof(key_points) / sizeof(key_points[0]);
    
    for (int j = 0; j < num_points; j++) {
        int i = key_points[j];
        float coeff = pwm_linearization_table[i];
        int pwm_val = (int)(coeff * 4095);
        float expected_watts = 1200.0f * coeff;
        
        Serial.printf("  %2d%% -> %.3f -> PWM:%4d -> 预期功率:%4.0fW\n", 
                      i, coeff, pwm_val, expected_watts);
    }
    
    Serial.println("[OPTIMIZED_PWM] 强力压制目标：");
    Serial.println("[OPTIMIZED_PWM] - 12%：约150W（不是700W）");
    Serial.println("[OPTIMIZED_PWM] - 14%：约210W（不是800W）");
    Serial.println("[OPTIMIZED_PWM] - 16%：约270W（不是1100W）");
    Serial.println("[OPTIMIZED_PWM] - 20%：约300W（25%功率上限）");
}

void OptimizedPWMControl::setPower(int power_percent) {
    power_percent = constrain(power_percent, 0, 100);
    
    // 应用增强的功率平滑
    int smoothed_power = enhancedPowerSmoothing(power_percent);
    
    // 应用功率变化率限制
    int limited_power = limitPowerChangeRate(smoothed_power);
    
    target_power = limited_power;
    
    // 详细调试信息，特别关注低功率区域
    float linear_coeff = linearizePWMPower(target_power);
    int pwm_value = calculatePWMValue();
    
    Serial.printf("[OPTIMIZED_PWM] 功率设置: %d%% -> 平滑: %d%% -> 限制: %d%%\n", 
                  power_percent, smoothed_power, limited_power);
    Serial.printf("[OPTIMIZED_PWM] 线性化: %.3f -> PWM值: %d (%.1f%%)\n", 
                  linear_coeff, pwm_value, (pwm_value / 4095.0f) * 100);
}

int OptimizedPWMControl::getCurrentPower() {
    return target_power;
}

void OptimizedPWMControl::update() {
    int pwm_value = calculatePWMValue();
    
    // 使用主程序中已有的PWM写入方式，确保兼容性
    ledcWriteChannel(pwm_channel, pwm_value);
}

void OptimizedPWMControl::setPWMFrequency(int frequency) {
    if (frequency >= 1000 && frequency <= 20000) {
        pwm_frequency = frequency;
        
        // 动态频率调整需要重新配置PWM通道
        // 使用主程序中相同的API
        ledcAttachChannel(OUT_FIR, pwm_frequency, pwm_resolution, pwm_channel);
        
        Serial.printf("[OPTIMIZED_PWM] PWM频率更新为: %dHz\n", pwm_frequency);
        Serial.println("[OPTIMIZED_PWM] 频率已动态更新");
    } else {
        Serial.println("[OPTIMIZED_PWM] 错误：PWM频率应在1000-20000Hz范围内");
    }
}

void OptimizedPWMControl::setSmoothingFactor(float factor) {
    smoothing_factor = constrain(factor, 0.05f, 0.5f);
    Serial.printf("[OPTIMIZED_PWM] 平滑因子设置为: %.3f\n", smoothing_factor);
}

void OptimizedPWMControl::setPowerChangeLimit(int max_change) {
    max_power_change_per_cycle = constrain(max_change, 1, 10);
    Serial.printf("[OPTIMIZED_PWM] 功率变化率限制: %d%%/周期\n", max_power_change_per_cycle);
}

void OptimizedPWMControl::setOptocouplerType(OptocouplerType type) {
    optocoupler_type = type;
    switchLinearizationTable();
    
    const char* type_name = (type == MOC3063_TYPE) ? "MOC3063" : "MOC3020";
    Serial.printf("[OPTIMIZED_PWM] 光耦类型切换为: %s\n", type_name);
    
    // 显示新光耦的特性
    Serial.println("[OPTIMIZED_PWM] 新光耦特性预览：");
    int preview_points[] = {8, 10, 12, 15, 18, 20};
    int num_preview = sizeof(preview_points) / sizeof(preview_points[0]);
    
    for (int i = 0; i < num_preview; i++) {
        int power = preview_points[i];
        float coeff = pwm_linearization_table[power];
        float expected_watts = 1200.0f * coeff;
        Serial.printf("  %2d%% -> %.3f -> 预期功率:%4.0fW\n", 
                      power, coeff, expected_watts);
    }
}

OptocouplerType OptimizedPWMControl::getOptocouplerType() {
    return optocoupler_type;
}

float OptimizedPWMControl::linearizePWMPower(int power_percent) {
    power_percent = constrain(power_percent, 0, 100);
    return pwm_linearization_table[power_percent];
}

int OptimizedPWMControl::enhancedPowerSmoothing(int new_power) {
    // 临时禁用复杂平滑算法，确保线性度
    // 只使用简单的3点移动平均，避免过度平滑
    
    static int simple_buffer[3] = {0, 0, 0};
    static int simple_index = 0;
    
    simple_buffer[simple_index] = new_power;
    simple_index = (simple_index + 1) % 3;
    
    int sum = simple_buffer[0] + simple_buffer[1] + simple_buffer[2];
    return sum / 3;
}

int OptimizedPWMControl::limitPowerChangeRate(int new_power) {
    // 临时禁用功率变化率限制，确保响应准确性
    // 直接返回输入值，不做限制
    last_output_power = new_power;
    return new_power;
}

int OptimizedPWMControl::calculatePWMValue() {
    if (target_power <= 0) {
        return 0;
    }
    
    if (target_power >= 100) {
        return 4095;
    }
    
    // 使用优化的线性化功率计算PWM值
    float linear_power = linearizePWMPower(target_power);
    return (int)(linear_power * 4095);
}

void OptimizedPWMControl::reset() {
    target_power = 0;
    last_output_power = 0;
    
    // 清空功率缓冲区
    for (int i = 0; i < 15; i++) {
        power_buffer[i] = 0;
    }
    buffer_index = 0;
    
    Serial.println("[OPTIMIZED_PWM] PWM控制器已重置");
}

void OptimizedPWMControl::printDebugInfo() {
    Serial.println("========== 优化PWM控制调试信息 ==========");
    Serial.printf("目标功率: %d%%\n", target_power);
    Serial.printf("线性化功率: %.3f\n", linearizePWMPower(target_power));
    Serial.printf("PWM频率: %dHz\n", pwm_frequency);
    Serial.printf("PWM值: %d (%.1f%%占空比)\n", calculatePWMValue(), 
                  (calculatePWMValue() / 4095.0f) * 100);
    Serial.printf("平滑因子: %.3f\n", smoothing_factor);
    Serial.printf("变化率限制: %d%%/周期\n", max_power_change_per_cycle);
    Serial.println("==========================================");
}

// 添加线性化表验证函数
void OptimizedPWMControl::printLinearizationTable() {
    Serial.println("========== PWM线性化查找表 ==========");
    Serial.println("功率% | 线性化系数 | PWM值 | 占空比%");
    for (int i = 0; i <= 100; i += 5) {
        float coeff = pwm_linearization_table[i];
        int pwm_val = (int)(coeff * 4095);
        float duty_cycle = coeff * 100;
        Serial.printf("%3d%% | %8.3f | %4d | %6.1f%%\n", 
                      i, coeff, pwm_val, duty_cycle);
    }
    Serial.println("=====================================");
}

// OptimizedPowerStabilityMonitor 实现
void OptimizedPowerStabilityMonitor::addPowerData(float power) {
    power_history[history_index] = power;
    history_index = (history_index + 1) % 30;
}

bool OptimizedPowerStabilityMonitor::isPowerStable() {
    float variation = getPowerVariation();
    return variation < stability_threshold;
}

float OptimizedPowerStabilityMonitor::getPowerVariation() {
    float sum = 0;
    float mean = getAveragePower();
    
    // 计算标准差
    float variance = 0;
    for (int i = 0; i < 30; i++) {
        variance += pow(power_history[i] - mean, 2);
    }
    
    return sqrt(variance / 30.0f);
}

float OptimizedPowerStabilityMonitor::getAveragePower() {
    float sum = 0;
    for (int i = 0; i < 30; i++) {
        sum += power_history[i];
    }
    return sum / 30.0f;
}

void OptimizedPowerStabilityMonitor::reset() {
    for (int i = 0; i < 30; i++) {
        power_history[i] = 0;
    }
    history_index = 0;
}