# MOC3063默认配置验证

## 修改内容

### 1. 代码修改
- **文件**: `optimized_pwm_control.cpp` 第8行
- **修改**: `optocoupler_type = MOC3063_TYPE;  // 默认使用MOC3063`
- **原值**: `optocoupler_type = MOC3020_TYPE;  // 默认使用MOC3020`

- **文件**: `optimized_pwm_control.h` 第20行  
- **修改**: `OptocouplerType optocoupler_type = MOC3063_TYPE;  // 当前光耦类型`
- **原值**: `OptocouplerType optocoupler_type = MOC3020_TYPE;  // 当前光耦类型`

### 2. 文档更新
- **文件**: `optocoupler_adaptation_guide.md`
- **更新**: 使用建议部分，明确标注项目现已默认使用MOC3063

## 验证方法

### 1. 编译验证
```bash
# 编译项目确保无语法错误
arduino-cli compile --fqbn esp32:esp32:esp32 coffee21-0710BEST.ino
```

### 2. 运行时验证
上传固件后，通过串口发送以下命令验证：

```bash
# 查看当前光耦类型（应该显示MOC3063）
PID;OPTOCOUPLER,?

# 对比两种光耦特性（验证MOC3063为当前默认）
PID;OPTOCOMPARE
```

### 3. 预期结果
- 系统启动时应显示：`当前光耦: MOC3063`
- 功率映射应使用MOC3063的特性曲线
- 相比MOC3020，MOC3063在相同设置下功率输出更低、更平缓

## MOC3063特性优势

### 与MOC3020对比
| 设置% | MOC3020功率 | MOC3063功率 | 改善效果 |
|-------|-------------|-------------|----------|
| 10%   | 150W        | 120W        | 更保守启动 |
| 12%   | 180W        | 144W        | 更强压制 |
| 15%   | 225W        | 180W        | 更平缓响应 |
| 18%   | 270W        | 216W        | 更可控 |
| 20%   | 300W        | 240W        | 更低上限 |

### 技术优势
1. **更高敏感度** - 响应更快
2. **更低触发电流** - 功耗更低  
3. **更好线性度** - 控制更精确
4. **更强功率压制** - 安全性更高

## 回退方法

如需切换回MOC3020，可以：

### 方法1：运行时切换
```bash
PID;OPTOCOUPLER,3020
```
注意：重启后会恢复到MOC3063

### 方法2：代码回退
将上述修改的两个文件中的 `MOC3063_TYPE` 改回 `MOC3020_TYPE`

## 修改完成确认

✅ 默认光耦类型已从MOC3020更改为MOC3063  
✅ 头文件和实现文件保持一致  
✅ 文档已更新反映新的默认配置  
✅ 保留了运行时切换功能  

现在项目将默认使用MOC3063光耦，提供更好的功率控制特性。
