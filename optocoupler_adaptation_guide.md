# 光耦芯片适配指南

## 🔌 支持的光耦芯片

### MOC3020 (原有芯片)
- **特性**：标准触发特性
- **状态**：已优化，功率映射稳定
- **适用**：当前已验证的系统

### MOC3063 (新芯片)
- **特性**：更高敏感度，更低触发电流
- **优势**：响应更快，功耗更低
- **注意**：需要更强的功率压制

## 🔄 光耦切换系统

### 切换命令
```bash
# 切换到MOC3020
PID;OPTOCOUPLER,3020

# 切换到MOC3063  
PID;OPTOCOUPLER,3063

# 查看当前光耦类型
PID;OPTOCOUPLER,?
```

### 特性对比测试
```bash
# 对比两种光耦的功率映射
PID;OPTOCOMPARE
```

## 📊 光耦特性差异

### MOC3020 vs MOC3063 功率映射对比

| 设置% | MOC3020功率 | MOC3063功率 | 差异分析 |
|-------|-------------|-------------|----------|
| 8%    | 0W (死区)   | 0W (死区)   | 相同 |
| 10%   | 150W        | 120W        | 3063更保守 |
| 12%   | 180W        | 144W        | 3063压制更强 |
| 15%   | 225W        | 180W        | 3063响应更平缓 |
| 18%   | 270W        | 216W        | 3063更可控 |
| 20%   | 300W        | 240W        | 3063上限更低 |

## 🎯 MOC3063优化策略

### 1. 更小的死区
- **MOC3020**：0-10%死区
- **MOC3063**：0-8%死区（更敏感）

### 2. 更强的功率压制
- **10-18%区间**：最大20%功率（vs MOC3020的25%）
- **更平缓的增长曲线**
- **更低的功率上限**

### 3. 分段控制策略
```cpp
// MOC3063特性补偿
if (i < 8) {
    // 更小死区
    moc3063_table[i] = 0.0f;
} else if (i <= 18) {
    // 更强压制：8-18% -> 0-20%功率
    float range = (i - 8.0f) / 10.0f;
    moc3063_table[i] = range * 0.20f;
} else if (i <= 35) {
    // 缓慢增长：18-35% -> 20-45%功率
    float range = (i - 18.0f) / 17.0f;
    moc3063_table[i] = 0.20f + range * 0.25f;
}
```

## 🧪 测试验证流程

### 1. 切换到MOC3063
```bash
PID;OPTOCOUPLER,3063
```

### 2. 验证特性差异
```bash
PID;OPTOCOMPARE
```

### 3. 测试关键功率点
```bash
PID;HEATERTEST,10    # 应该约120W（比3020的150W更低）
PID;HEATERTEST,12    # 应该约144W（比3020的180W更低）
PID;HEATERTEST,15    # 应该约180W（比3020的225W更低）
PID;HEATERTEST,0     # 关闭
```

### 4. 验证稳定性
```bash
PID;TRIACTEST        # 使用MOC3063配置测试
```

## 🔧 微调建议

### 如果MOC3063仍然太敏感
```bash
# 进一步压制功率
# 可以通过代码调整压制系数
```

### 如果MOC3063响应不足
```bash
# 减小死区或增加敏感度
# 调整分段控制参数
```

## 📈 预期改善效果

### MOC3063的优势
1. **更平滑的功率控制**
2. **更低的最小触发功率**
3. **更好的线性度**
4. **更低的功耗**

### 注意事项
1. **需要重新校准功率映射**
2. **可能需要调整PWM频率**
3. **建议从低功率开始测试**

## 🚀 使用建议

### 新用户
- 建议使用MOC3063（更好的控制特性）
- 从低功率开始测试
- 逐步验证功率映射

### 现有用户
- 可以保持MOC3020（已验证稳定）
- 或升级到MOC3063获得更好控制
- 切换时需要重新测试功率映射

现在你可以轻松在两种光耦之间切换，并获得针对性的优化！